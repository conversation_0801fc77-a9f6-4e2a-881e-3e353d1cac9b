# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ai-gateway-helm.controller.fullname" . }}
  labels:
    {{- include "ai-gateway-helm.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.controller.replicaCount }}
  selector:
    matchLabels:
      {{- include "ai-gateway-helm.controller.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.controller.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ai-gateway-helm.controller.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.controller.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "ai-gateway-helm.controller.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.controller.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.controller.securityContext | nindent 12 }}
          image: "{{ .Values.controller.image.repository }}:{{ .Values.controller.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.controller.image.pullPolicy }}
          ports:
            - containerPort: 9443
            - containerPort: 1063
            - containerPort: 9090
          args:
            - -logLevel={{ .Values.controller.logLevel }}
            - --extProcImage={{ .Values.extProc.repository }}:{{ .Values.extProc.tag | default .Chart.AppVersion }}
            - --extProcLogLevel={{ .Values.extProc.logLevel }}
            - --tlsCertDir=/certs
            - --tlsCertName={{ .Values.controller.mutatingWebhook.tlsCertName }}
            - --tlsKeyName={{ .Values.controller.mutatingWebhook.tlsKeyName }}
            - --envoyGatewayNamespace={{ .Values.controller.envoyGatewayNamespace }}
            {{- if .Values.controller.leaderElection.enabled }}
            - --enableLeaderElection=true
            {{- end }}
          livenessProbe:
            grpc:
              port: 1063
            initialDelaySeconds: 5
            periodSeconds: 2
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          {{- if .Values.controller.podEnv }}
          {{- range $key, $val := .Values.controller.podEnv }}
            - name: {{ $key }}
              value: {{ $val }}
          {{- end }}
          {{- end }}
          readinessProbe:
            grpc:
              port: 1063
            initialDelaySeconds: 5
            periodSeconds: 2
          resources:
            {{- toYaml .Values.controller.resources | nindent 12 }}
          volumeMounts:
            - mountPath: /certs
              name: certs
              readOnly: true
          {{- if .Values.controller.volumes }}
            {{- range $volume := .Values.controller.volumes }}
            - mountPath: {{ $volume.mountPath }}
              name: {{ $volume.name }}
              {{- if $volume.subPath }}
              subPath: {{ $volume.subPath }}
              {{- end }}
            {{- end}}
          {{- end }}
      volumes:
        - name: certs
          secret:
            secretName: {{ .Values.controller.mutatingWebhook.tlsCertSecretName }}
      {{- if .Values.controller.volumes }}
        {{- range $volume := .Values.controller.volumes }}
        - name: {{ $volume.name }}
          configMap:
            defaultMode: {{ $volume.configmap.defaultMode }}
            name: {{ $volume.configmap.name }}
        {{- end }}
      {{- end }}
      {{- with .Values.controller.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
