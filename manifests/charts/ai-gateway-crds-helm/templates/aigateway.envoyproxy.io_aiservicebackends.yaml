# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  name: aiservicebackends.aigateway.envoyproxy.io
spec:
  group: aigateway.envoyproxy.io
  names:
    kind: AIServiceBackend
    listKind: AIServiceBackendList
    plural: aiservicebackends
    singular: aiservicebackend
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[-1:].type
      name: Status
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          AIServiceBackend is a resource that represents a single backend for AIGatewayRoute.
          A backend is a service that handles traffic with a concrete API specification.

          A AIServiceBackend is "attached" to a Backend which is either a k8s Service or a Backend resource of the Envoy Gateway.

          When a backend with an attached AIServiceBackend is used as a routing target in the AIGatewayRoute (more precisely, the
          HTTPRouteSpec defined in the AIGatewayRoute), the ai-gateway will generate the necessary configuration to do
          the backend specific logic in the final HTTPRoute.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the details of AIServiceBackend.
            properties:
              backendRef:
                description: |-
                  BackendRef is the reference to the Backend resource that this AIServiceBackend corresponds to.

                  A backend must be a Backend resource of Envoy Gateway. Note that k8s Service will be supported
                  as a backend in the future.

                  This is required to be set.
                properties:
                  group:
                    default: ""
                    description: |-
                      Group is the group of the referent. For example, "gateway.networking.k8s.io".
                      When unspecified or empty string, core API group is inferred.
                    maxLength: 253
                    pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                    type: string
                  kind:
                    default: Service
                    description: |-
                      Kind is the Kubernetes resource kind of the referent. For example
                      "Service".

                      Defaults to "Service" when not specified.

                      ExternalName services can refer to CNAME DNS records that may live
                      outside of the cluster and as such are difficult to reason about in
                      terms of conformance. They also may not be safe to forward to (see
                      CVE-2021-25740 for more information). Implementations SHOULD NOT
                      support ExternalName Services.

                      Support: Core (Services with a type other than ExternalName)

                      Support: Implementation-specific (Services with type ExternalName)
                    maxLength: 63
                    minLength: 1
                    pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                    type: string
                  name:
                    description: Name is the name of the referent.
                    maxLength: 253
                    minLength: 1
                    type: string
                  namespace:
                    description: |-
                      Namespace is the namespace of the backend. When unspecified, the local
                      namespace is inferred.

                      Note that when a namespace different than the local namespace is specified,
                      a ReferenceGrant object is required in the referent namespace to allow that
                      namespace's owner to accept the reference. See the ReferenceGrant
                      documentation for details.

                      Support: Core
                    maxLength: 63
                    minLength: 1
                    pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                    type: string
                  port:
                    description: |-
                      Port specifies the destination port number to use for this resource.
                      Port is required when the referent is a Kubernetes Service. In this
                      case, the port number is the service port number, not the target port.
                      For other resources, destination port might be derived from the referent
                      resource or this field.
                    format: int32
                    maximum: 65535
                    minimum: 1
                    type: integer
                required:
                - name
                type: object
                x-kubernetes-validations:
                - message: Must have port for Service reference
                  rule: '(size(self.group) == 0 && self.kind == ''Service'') ? has(self.port)
                    : true'
              backendSecurityPolicyRef:
                description: |-
                  BackendSecurityPolicyRef is the name of the BackendSecurityPolicy resources this backend
                  is being attached to.
                properties:
                  group:
                    description: |-
                      Group is the group of the referent. For example, "gateway.networking.k8s.io".
                      When unspecified or empty string, core API group is inferred.
                    maxLength: 253
                    pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                    type: string
                  kind:
                    description: Kind is kind of the referent. For example "HTTPRoute"
                      or "Service".
                    maxLength: 63
                    minLength: 1
                    pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                    type: string
                  name:
                    description: Name is the name of the referent.
                    maxLength: 253
                    minLength: 1
                    type: string
                required:
                - group
                - kind
                - name
                type: object
              schema:
                description: |-
                  APISchema specifies the API schema of the output format of requests from
                  Envoy that this AIServiceBackend can accept as incoming requests.
                  Based on this schema, the ai-gateway will perform the necessary transformation for
                  the pair of AIGatewayRouteSpec.APISchema and AIServiceBackendSpec.APISchema.

                  This is required to be set.
                properties:
                  name:
                    description: Name is the name of the API schema of the AIGatewayRoute
                      or AIServiceBackend.
                    enum:
                    - OpenAI
                    - AWSBedrock
                    - AzureOpenAI
                    type: string
                  version:
                    description: |-
                      Version is the version of the API schema.

                      When the name is set to "OpenAI", this equals to the prefix of the OpenAI API endpoints, and
                      this defaults to "v1" if not set. For example, "chat completions" API endpoint will be
                      "/v1/chat/completions" if the version is set to "v1".

                      This is especially useful when routing to the backend that has an OpenAI compatible API but has a different
                      versioning scheme. For example, Gemini OpenAI compatible API (https://ai.google.dev/gemini-api/docs/openai) uses
                      "/v1beta/openai" version prefix. Another example is that Cohere AI (https://docs.cohere.com/v2/docs/compatibility-api)
                      uses "/compatibility/v1" version prefix. On the other hand, DeepSeek (https://api-docs.deepseek.com/) doesn't
                      use version prefix, so the version can be set to an empty string.

                      When the name is set to AzureOpenAI, this version maps to "API Version" in the
                      Azure OpenAI API documentation (https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#rest-api-versioning).
                    type: string
                required:
                - name
                type: object
            required:
            - backendRef
            - schema
            type: object
          status:
            description: Status defines the status details of the AIServiceBackend.
            properties:
              conditions:
                description: |-
                  Conditions is the list of conditions by the reconciliation result.
                  Currently, at most one condition is set.

                  Known .status.conditions.type are: "Accepted", "NotAccepted".
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
