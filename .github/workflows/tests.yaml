name: Tests
on:
  pull_request:
    branches:
      - 'main'
      # Release branches are like "release/v0.1", "release/v0.2", etc. where we backport the changes to non EOL versions.
      # The branch will be created from the main branch after the initial release tag is cut. For example, when we cut v0.8.0 release,
      # we will create a branch "release/v0.8" from the main branch. For rc release, we simply iterate on main branch.
      #
      # See RELEASES.md for more details.
      - 'release/**'
    paths-ignore:
      - '**/*.md'
      - 'site/**'
      - 'netlify.toml'

  push:
    branches:
      - 'main'
      - 'release/**'
    paths-ignore:
      - '**/*.md'
      - 'site/**'
      - 'netlify.toml'

concurrency:
  # https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#example-using-concurrency-to-cancel-any-in-progress-job-or-run
  group: ${{ github.ref }}-${{ github.workflow }}-${{ github.actor }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read
  packages: write

jobs:
  unittest:
    name: Unit Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: unittest-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - env:
          TEST_AWS_ACCESS_KEY_ID: ${{ secrets.AWS_BEDROCK_USER_AWS_ACCESS_KEY_ID }}
          TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_BEDROCK_USER_AWS_SECRET_ACCESS_KEY }}
          TEST_OPENAI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_OPENAI_API_KEY }}
        run: make test-coverage

  test_crdcel:
    name: CRD CEL Validation Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: celvalidation-test-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - run: make test-crdcel

  test_controller:
    name: Controller Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: controller-test-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - run: make test-controller

  test_extproc:
    name: External Processor Test (Envoy ${{ matrix.name }})
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: v1.34
            envoy_version: envoyproxy/envoy:v1.34-latest
          - name: latest
            envoy_version: envoyproxy/envoy-dev:latest
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        if: github.event_name != 'pull_request_target'
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
        if: github.event_name == 'pull_request_target'
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: extproc-tests-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - name: Install Envoy
        run: |
          export ENVOY_BIN_DIR=$HOME/envoy/bin
          mkdir -p $ENVOY_BIN_DIR
          docker run -v $ENVOY_BIN_DIR:/tmp/ci -w /tmp/ci \
          --entrypoint /bin/cp ${{ matrix.envoy_version }} /usr/local/bin/envoy .
          echo $ENVOY_BIN_DIR >> $GITHUB_PATH
      - env:
          TEST_AWS_ACCESS_KEY_ID: ${{ secrets.AWS_BEDROCK_USER_AWS_ACCESS_KEY_ID }}
          TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_BEDROCK_USER_AWS_SECRET_ACCESS_KEY }}
          TEST_OPENAI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_OPENAI_API_KEY }}
          TEST_GEMINI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_GEMINI_API_KEY }}
        run: make test-extproc

  test_e2e:
    # Not all the cases in E2E require secrets, so we run for all the events.
    name: E2E Test (Envoy Gateway ${{ matrix.name }})
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: latest
            envoy_gateway_version: v0.0.0-latest
          - name: 1.4.0
            envoy_gateway_version: 1.4.0
    steps:
      - uses: actions/checkout@v4
        if: github.event_name != 'pull_request_target'
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
        if: github.event_name == 'pull_request_target'
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/.cache/golangci-lint
            ~/go/pkg/mod
            ~/go/bin
          key: e2e-test-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - uses: docker/setup-buildx-action@v3
      - env:
          EG_VERSION: ${{ matrix.envoy_gateway_version }}
          TEST_AWS_ACCESS_KEY_ID: ${{ secrets.AWS_BEDROCK_USER_AWS_ACCESS_KEY_ID }}
          TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_BEDROCK_USER_AWS_SECRET_ACCESS_KEY }}
          TEST_OPENAI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_OPENAI_API_KEY }}
          TEST_GEMINI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_GEMINI_API_KEY }}
        run: make test-e2e

  docker_push:
    # Docker builds are verified in test_e2e job, so we only need to push the images when the event is a push event.
    if: github.event_name == 'push'
    name: Push Docker Images
    needs: [unittest, test_crdcel, test_controller, test_extproc, test_e2e]
    uses: ./.github/workflows/docker_builds_template.yaml
    secrets: inherit

  helm_push:
    name: Push Helm chart
    # Only push the Helm chart to the GHR when merged into the main branch.
    if: github.event_name == 'push'
    needs: [docker_push]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Login into DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}
      - run: |
          make helm-push HELM_CHART_VERSION=v0.0.0-latest
          make helm-push HELM_CHART_VERSION=0.0.0-latest
